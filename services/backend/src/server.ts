// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import multer from 'multer';
import multerS3 from 'multer-s3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import { pipeline } from 'stream/promises';
import * as tmp from 'tmp';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';

import { sendShareEmail, EmailShareRequest } from './lib/email-service';
import { analyticsService, AnalyticsEventType } from './lib/analytics-service';
import { CompressionRouter, CompressionMethod } from './compression-router';
import { EC2UploadService } from './ec2-upload-service';
import { analytics } from './ec2-analytics';
import { ChunkManager } from './chunk-manager';
import {
  securityHeaders,
  requestLogger,
  uploadRateLimit,
  chunkUploadRateLimit,
  finalizeUploadRateLimit,
  apiRateLimit,
  validateFile,
  healthCheck,
  errorHandler
} from './security-middleware';

// Utility function for safe stream operations with timeout and error handling
async function safeStreamOperation<T>(
  operation: () => Promise<T>,
  operationName: string,
  timeoutMs: number = 1800000 // 30 minutes default timeout for large files
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`${operationName} timed out after ${timeoutMs}ms`));
    }, timeoutMs);

    operation()
      .then((result) => {
        clearTimeout(timeout);
        resolve(result);
      })
      .catch((error) => {
        clearTimeout(timeout);
        console.error(`${operationName} failed:`, error);
        reject(error);
      });
  });
}


const app = express();
const port = process.env.PORT || 3000;

// Promisify exec for async/await usage
const execAsync = promisify(exec);

// Initialize compression router
const SCRIPTS_DIR = process.env.SCRIPTS_DIR || path.join(__dirname, '../../../scripts');
const compressionRouter = new CompressionRouter(SCRIPTS_DIR);

// Configure AWS S3 Client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY ? {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  } : undefined,
  requestHandler: {
    requestTimeout: 1800000, // 30 minutes for large file operations
    connectionTimeout: 60000, // 1 minute connection timeout
  },
});

// S3 bucket names
const UPLOAD_BUCKET = process.env.UPLOAD_BUCKET || 'fasttransfer-upload-dev';
const COMPRESSED_BUCKET = process.env.COMPRESSED_BUCKET || 'fasttransfer-compressed-dev';
const DECOMPRESSED_BUCKET = process.env.DECOMPRESSED_BUCKET || 'fasttransfer-decompressed-dev';

// Check AWS configuration
if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.warn('⚠️  AWS credentials not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env file');
  console.warn('   S3 operations will fail until credentials are properly configured.');
}

// Configure multer for S3 file uploads
const storage = multerS3({
  s3: s3Client,
  bucket: UPLOAD_BUCKET,
  key: (req: any, file, cb) => {
    const transferId = req.body?.transferId || uuidv4();
    const key = `uploads/${transferId}/${file.originalname}`;
    cb(null, key);
  },
  metadata: (req: any, file, cb) => {
    const transferId = req.body?.transferId || uuidv4();
    cb(null, {
      'transfer-id': transferId,
      'original-filename': file.originalname,
      'upload-timestamp': Date.now().toString()
    });
  }
});

// Function to compress file using ZMT with S3 streaming optimization and multi-script support
async function compressFileWithZMT(s3Key: string, transferId: string, fileName?: string): Promise<{ compressedS3Key: string; compressionRatio: number; compressedSize: number; compressionTime: number; compressionMethod: string }> {
  // Create temporary files using tmp library for better cleanup
  const tempDir = tmp.dirSync({ prefix: `zmt-compress-${transferId}-`, unsafeCleanup: true });
  const inputFileName = fileName || path.basename(s3Key);
  const inputFilePath = path.join(tempDir.name, inputFileName);
  const compressedFileName = `${transferId}.zmt`;
  const compressedPath = path.join(tempDir.name, compressedFileName);
  const compressedS3Key = `compressed/${transferId}/${compressedFileName}`;

  // Determine compression method based on file type
  const compressionMethod = compressionRouter.getCompressionMethod(inputFileName);

  try {
    console.log(`🔄 Starting optimized ZMT compression for ${s3Key}`);

    // Stream file from S3 directly to temp file
    console.log(`📥 Streaming file from S3: ${s3Key}`);
    const getObjectCommand = new GetObjectCommand({
      Bucket: UPLOAD_BUCKET,
      Key: s3Key,
    });

    const response = await s3Client.send(getObjectCommand);
    const originalSize = response.ContentLength || 0;

    // Use safe stream operation with timeout and error handling
    if (response.Body) {
      await safeStreamOperation(async () => {
        const writeStream = fs.createWriteStream(inputFilePath);
        await pipeline(response.Body as any, writeStream);
      }, `S3 download for ${s3Key}`, 1800000); // 30 minute timeout for large files
      console.log(`✅ File streamed successfully (${originalSize} bytes)`);
    } else {
      throw new Error('No file body received from S3');
    }

    // Use compression router for multi-script compression
    console.log(`🗜️ Using compression method: ${compressionMethod} for file: ${inputFileName}`);
    const compressionResult = await compressionRouter.compressFiles([inputFilePath], compressedPath, compressionMethod);

    const compressedSize = compressionResult.compressedSize;

    // Stream compressed file directly to S3 with safe operation
    console.log(`📤 Streaming compressed file to S3: ${compressedS3Key}`);

    await safeStreamOperation(async () => {
      const compressedFileStream = fs.createReadStream(compressedPath);
      const uploadCommand = new PutObjectCommand({
        Bucket: COMPRESSED_BUCKET,
        Key: compressedS3Key,
        Body: compressedFileStream,
        ContentType: 'application/octet-stream',
        Metadata: {
          'transfer-id': transferId,
          'original-filename': inputFileName,
          'original-size': originalSize.toString(),
          'compressed-size': compressedSize.toString(),
          'compression-timestamp': Date.now().toString()
        }
      });

      await s3Client.send(uploadCommand);
    }, `S3 upload for ${compressedS3Key}`, 1800000); // 30 minute timeout for large file uploads

    // Use compression ratio from compression result (already calculated as decimal 0-1)
    const compressionRatio = compressionResult.compressionRatio;

    console.log(`✅ Compression completed: ${compressionResult.originalSize} bytes -> ${compressedSize} bytes (${(compressionResult.compressionRatio * 100).toFixed(2)}% reduction) in ${compressionResult.compressionTime}ms using ${compressionResult.method}`);

    // Automatic cleanup using tmp library
    tempDir.removeCallback();
    console.log(`🧹 Temporary files cleaned up automatically`);

    return {
      compressedS3Key,
      compressionRatio,
      compressedSize,
      compressionTime: compressionResult.compressionTime,
      compressionMethod: compressionResult.method
    };
  } catch (error) {
    console.error('❌ ZMT compression failed:', error);
    // Automatic cleanup on error using tmp library
    try {
      tempDir.removeCallback();
      console.log(`🧹 Temporary files cleaned up after error`);
    } catch (cleanupError) {
      console.error('Error during automatic cleanup:', cleanupError);
    }
    throw error;
  }
}

// Function to decompress ZMT file with S3 streaming optimization
async function decompressZMTFile(compressedS3Key: string, transferId: string): Promise<string[]> {
  // Create temporary directory using tmp library for better cleanup
  const tempDir = tmp.dirSync({ prefix: `zmt-decompress-${transferId}-`, unsafeCleanup: true });
  const compressedFileName = path.basename(compressedS3Key);
  const compressedFilePath = path.join(tempDir.name, compressedFileName);

  // Get the absolute path to the ZMT binary
  const zmtBinaryPath = path.join(__dirname, '../../../scripts/zmt');

  try {
    console.log(`🔄 Starting optimized ZMT decompression for ${compressedS3Key}`);

    // Stream compressed file from S3 directly to temp file
    console.log(`📥 Streaming compressed file from S3: ${compressedS3Key}`);
    const getObjectCommand = new GetObjectCommand({
      Bucket: COMPRESSED_BUCKET,
      Key: compressedS3Key,
    });

    const response = await s3Client.send(getObjectCommand);

    // Use safe stream operation with timeout and error handling
    if (response.Body) {
      await safeStreamOperation(async () => {
        const writeStream = fs.createWriteStream(compressedFilePath);
        await pipeline(response.Body as any, writeStream);
      }, `S3 download for compressed file ${compressedS3Key}`, 1800000); // 30 minute timeout for large files
      console.log(`✅ Compressed file streamed successfully`);
    } else {
      throw new Error('No compressed file body received from S3');
    }

    // Run ZMT extraction with force overwrite: zmt x archive.zmt -force
    const command = `cd "${tempDir.name}" && "${zmtBinaryPath}" x "${compressedFileName}" -force`;
    console.log(`🗜️ Running ZMT decompression: ${command}`);

    const { stdout, stderr } = await execAsync(command);
    console.log('ZMT decompression output:', stdout);
    if (stderr) {
      console.log('ZMT decompression stderr:', stderr);
    }

    // List files in the temp directory to see what was extracted
    const extractedFiles: string[] = [];
    const files = fs.readdirSync(tempDir.name);

    for (const file of files) {
      const filePath = path.join(tempDir.name, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile() && file !== compressedFileName) { // Exclude the compressed file itself
        extractedFiles.push(file);

        // Stream decompressed file directly to S3 with safe operation
        const decompressedS3Key = `decompressed/${transferId}/${file}`;
        console.log(`📤 Streaming decompressed file to S3: ${decompressedS3Key}`);

        await safeStreamOperation(async () => {
          const fileStream = fs.createReadStream(filePath);
          const uploadCommand = new PutObjectCommand({
            Bucket: DECOMPRESSED_BUCKET,
            Key: decompressedS3Key,
            Body: fileStream,
            Metadata: {
              'transfer-id': transferId,
              'original-filename': file,
              'decompression-timestamp': Date.now().toString()
            }
          });

          await s3Client.send(uploadCommand);
        }, `S3 upload for decompressed file ${file}`, 1800000); // 30 minute timeout for large files
        console.log(`✅ File ${file} uploaded successfully`);
      }
    }

    console.log(`✅ Decompression completed. Extracted files: ${extractedFiles.join(', ')}`);

    // Automatic cleanup using tmp library
    tempDir.removeCallback();
    console.log(`🧹 Temporary files cleaned up automatically`);

    return extractedFiles;
  } catch (error) {
    console.error('❌ ZMT decompression failed:', error);
    // Automatic cleanup on error using tmp library
    try {
      tempDir.removeCallback();
      console.log(`🧹 Temporary files cleaned up after error`);
    } catch (cleanupError) {
      console.error('Error during automatic cleanup:', cleanupError);
    }
    throw error;
  }
}

const upload = multer({ storage });

// Security middleware
app.use(securityHeaders);
app.use(requestLogger);

// Apply API rate limiting to all routes except status polling and chunk upload endpoints
app.use((req, res, next) => {
  // Skip rate limiting for status polling endpoints to prevent polling issues
  if (req.path.includes('/status') || req.path.includes('/health')) {
    return next();
  }
  // Skip rate limiting for chunk uploads during development/testing
  if (req.path === '/api/upload-chunk') {
    return next();
  }
  return apiRateLimit(req, res, next);
});

// CORS and body parsing middleware
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:5174',
    'http://127.0.0.1:3000',
    'http://transfer.yeehawboost.com',
    'https://transfer.yeehawboost.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json({ limit: '100gb' })); // Support large JSON payloads
app.use(express.urlencoded({ limit: '100gb', extended: true })); // Support large form data

// Set server timeout for large file uploads (30 minutes)
app.use((req, res, next) => {
  req.setTimeout(30 * 60 * 1000); // 30 minutes
  res.setTimeout(30 * 60 * 1000); // 30 minutes
  next();
});

// In-memory storage for demo purposes (in production, this would be DynamoDB)
interface Transfer {
  transferId: string;
  filename: string;
  originalName: string;
  size: number;
  compressedSize?: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
  createdAt: Date;
  s3Key?: string; // S3 key for original file
  compressedS3Key?: string; // S3 key for compressed file
  expiresAt?: number;
  downloadLimit?: number;
  downloadCount?: number;
  password?: string;
  // New compression metadata fields
  compressionTime?: number; // Time taken for compression in milliseconds
  compressionMethod?: string; // Method used for compression (e.g., 'basic_zmt', 'video_mp4', etc.)
}

const transfers: Map<string, Transfer> = new Map();

// Routes

// Initialize EC2 Upload Service
const ec2UploadService = new EC2UploadService();

// Initialize Chunk Manager for chunked uploads
const chunkManager = new ChunkManager();

// Direct EC2 Upload endpoint - NEW ARCHITECTURE
app.post('/api/upload-direct', uploadRateLimit, ec2UploadService.getUploadMiddleware(), validateFile, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    console.log('📁 Direct upload received:', {
      filename: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
      path: req.file.path,
      providedTransferId: req.body.transferId
    });

    // Process the upload: compress locally and upload to S3
    const result = await ec2UploadService.processUpload(req.file.path, req.file.originalname, req.body.transferId);

    // Store transfer information for status tracking
    const transfer: Transfer = {
      transferId: result.transferId,
      filename: result.s3Key,
      originalName: result.originalFilename,
      size: result.originalSize,
      compressedSize: result.compressedSize,
      status: 'ready', // EC2 upload service completes processing immediately
      downloadUrl: result.downloadUrl,
      compressionRatio: result.compressionRatio,
      createdAt: new Date(),
      compressedS3Key: result.s3Key,
      expiresAt: result.expiresAt,
      compressionTime: result.compressionTime,
      compressionMethod: 'ec2_direct'
    };

    transfers.set(result.transferId, transfer);

    // Log analytics
    console.log('📊 Upload analytics:', {
      transferId: result.transferId,
      originalSize: result.originalSize,
      compressedSize: result.compressedSize,
      compressionRatio: result.compressionRatio,
      compressionTime: result.compressionTime
    });

    res.json({
      success: true,
      transferId: result.transferId,
      file: {
        originalFilename: result.originalFilename,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        compressionTime: result.compressionTime,
        s3Key: result.s3Key
      },
      download: {
        url: result.downloadUrl,
        expiresAt: result.expiresAt,
        expiresAtFormatted: new Date(result.expiresAt).toISOString()
      },
      processing: {
        compressionTime: result.compressionTime,
        uploadTime: Date.now() - Date.parse(req.headers['x-upload-start'] as string || '0'),
        totalTime: Date.now() - Date.parse(req.headers['x-upload-start'] as string || '0')
      },
      message: 'File uploaded and compressed successfully'
    });

  } catch (error) {
    console.error('❌ Direct upload error:', error);
    res.status(500).json({
      error: 'Upload processing failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Chunked Upload Endpoints - NEW CHUNKING ARCHITECTURE

// Initialize chunked upload
app.post('/api/upload-init', uploadRateLimit, async (req, res) => {
  try {
    const { originalFilename, totalSize, transferId } = req.body;

    if (!originalFilename || !totalSize || !transferId) {
      return res.status(400).json({
        error: 'Missing required fields: originalFilename, totalSize, transferId'
      });
    }

    console.log(`🚀 Initializing chunked upload: ${originalFilename} (${(totalSize / 1024 / 1024).toFixed(2)}MB)`);

    const metadata = await chunkManager.initializeUpload(transferId, originalFilename, totalSize);

    res.json({
      success: true,
      transferId,
      totalChunks: metadata.totalChunks,
      chunkSize: metadata.chunkSize,
      message: `Initialized upload for ${metadata.totalChunks} chunks`
    });

  } catch (error) {
    console.error('❌ Upload initialization error:', error);
    res.status(500).json({
      error: 'Failed to initialize upload',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Upload individual chunk
const chunkUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: ChunkManager.CHUNK_SIZE + 1024, // Allow slight overhead
    files: 1
  }
});

// Temporarily disable rate limiting for chunk uploads during development/testing
app.post('/api/upload-chunk', chunkUpload.single('chunk'), async (req, res) => {
  try {
    const { transferId, chunkIndex } = req.body;

    if (!req.file || !transferId || chunkIndex === undefined) {
      return res.status(400).json({
        error: 'Missing required fields: chunk file, transferId, chunkIndex'
      });
    }

    const chunkIndexNum = parseInt(chunkIndex);
    if (isNaN(chunkIndexNum)) {
      return res.status(400).json({ error: 'Invalid chunk index' });
    }

    console.log(`📤 Receiving chunk ${chunkIndexNum} for ${transferId} (${(req.file.size / 1024 / 1024).toFixed(2)}MB)`);

    const result = await chunkManager.storeChunk(transferId, chunkIndexNum, req.file.buffer);

    if (!result.success) {
      return res.status(400).json({
        error: 'Failed to store chunk',
        details: result.error
      });
    }

    res.json({
      success: true,
      chunkIndex: result.chunkIndex,
      uploadedChunks: result.uploadedChunks,
      totalChunks: result.totalChunks,
      isComplete: result.isComplete,
      progress: (result.uploadedChunks / result.totalChunks * 100).toFixed(2)
    });

  } catch (error) {
    console.error('❌ Chunk upload error:', error);
    res.status(500).json({
      error: 'Failed to upload chunk',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Finalize chunked upload and process file
app.post('/api/upload-finalize', finalizeUploadRateLimit, async (req, res) => {
  try {
    const { transferId } = req.body;

    if (!transferId) {
      return res.status(400).json({ error: 'Missing transferId' });
    }

    console.log(`🔗 Finalizing chunked upload: ${transferId}`);

    // Check if all chunks are uploaded
    const metadata = chunkManager.getTransferStatus(transferId);
    if (!metadata) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    if (metadata.uploadedChunks.size !== metadata.totalChunks) {
      return res.status(400).json({
        error: 'Incomplete upload',
        uploadedChunks: metadata.uploadedChunks.size,
        totalChunks: metadata.totalChunks
      });
    }

    // Reassemble file
    const reassembledPath = await chunkManager.reassembleFile(transferId);

    // Process the reassembled file through existing compression pipeline
    const result = await ec2UploadService.processUpload(reassembledPath, metadata.originalFilename, transferId);

    // Store transfer information for status tracking
    const transfer: Transfer = {
      transferId: result.transferId,
      filename: result.s3Key,
      originalName: result.originalFilename,
      size: result.originalSize,
      compressedSize: result.compressedSize,
      status: 'ready',
      downloadUrl: result.downloadUrl,
      compressionRatio: result.compressionRatio,
      createdAt: new Date(),
      compressedS3Key: result.s3Key,
      expiresAt: result.expiresAt,
      compressionTime: result.compressionTime,
      compressionMethod: 'chunked_upload'
    };

    transfers.set(result.transferId, transfer);

    // Cleanup chunks and reassembled file
    await chunkManager.cleanupTransfer(transferId);

    // Clean up reassembled file
    try {
      await fs.promises.unlink(reassembledPath);
    } catch (error) {
      console.warn('Failed to cleanup reassembled file:', error);
    }

    console.log(`✅ Chunked upload completed: ${result.originalFilename}`);

    res.json({
      success: true,
      transferId: result.transferId,
      file: {
        originalFilename: result.originalFilename,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        compressionTime: result.compressionTime,
        s3Key: result.s3Key
      },
      download: {
        url: result.downloadUrl,
        expiresAt: result.expiresAt,
        expiresAtFormatted: new Date(result.expiresAt).toISOString()
      },
      processing: {
        compressionTime: result.compressionTime,
        method: 'chunked_upload'
      },
      message: 'Chunked upload completed and file processed successfully'
    });

  } catch (error) {
    console.error('❌ Upload finalization error:', error);

    // Cleanup on error
    try {
      await chunkManager.cleanupTransfer(req.body.transferId);
    } catch (cleanupError) {
      console.error('Failed to cleanup after error:', cleanupError);
    }

    res.status(500).json({
      error: 'Failed to finalize upload',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get chunk upload progress
app.get('/api/upload-progress/:transferId', async (req, res) => {
  try {
    const { transferId } = req.params;

    const progress = chunkManager.getUploadProgress(transferId);
    if (!progress) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    const metadata = chunkManager.getTransferStatus(transferId);

    res.json({
      transferId,
      progress: {
        uploaded: progress.uploaded,
        total: progress.total,
        percentage: progress.percentage
      },
      metadata: {
        originalFilename: metadata?.originalFilename,
        totalSize: metadata?.totalSize,
        chunkSize: metadata?.chunkSize,
        createdAt: metadata?.createdAt,
        lastActivity: metadata?.lastActivity
      }
    });

  } catch (error) {
    console.error('❌ Progress check error:', error);
    res.status(500).json({
      error: 'Failed to get progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/api/health', healthCheck);

// EC2 Analytics endpoint
app.get('/api/analytics/ec2', async (req, res) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    const stats = await analytics.getUploadStats(hours);

    res.json({
      success: true,
      timeframe: `${hours} hours`,
      stats
    });
  } catch (error) {
    console.error('❌ Analytics endpoint error:', error);
    res.status(500).json({
      error: 'Failed to retrieve analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Original upload endpoint (kept for backward compatibility)
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const transferId = req.body.transferId || uuidv4();
    const s3File = req.file as any; // multer-s3 adds additional properties

    // Get actual file size from S3 since multer-s3 sometimes returns 0
    let actualFileSize = req.file.size;
    if (actualFileSize === 0) {
      try {
        const headCommand = new HeadObjectCommand({
          Bucket: UPLOAD_BUCKET,
          Key: s3File.key
        });
        const headResult = await s3Client.send(headCommand);
        actualFileSize = (headResult as any).ContentLength || 0;
        console.log(`Retrieved actual file size from S3: ${actualFileSize} bytes`);
      } catch (error) {
        console.error('Failed to get file size from S3:', error);
        // Fall back to reported size even if it's 0
      }
    }

    const transfer: Transfer = {
      transferId,
      filename: s3File.key, // S3 key
      originalName: req.file.originalname,
      size: actualFileSize,
      status: 'uploading',
      createdAt: new Date(),
      s3Key: s3File.key // Store S3 key instead of file path
    };

    transfers.set(transferId, transfer);

    // Start analytics tracking
    analyticsService.startPerformanceTracking(transferId, actualFileSize);
    analyticsService.trackEvent(
      AnalyticsEventType.UPLOAD_COMPLETED,
      {
        fileName: req.file.originalname,
        fileSize: actualFileSize,
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip
      },
      transferId
    );

    // Start real ZMT compression
    setTimeout(async () => {
      const currentTransfer = transfers.get(transferId);
      if (currentTransfer) {
        currentTransfer.status = 'compressing';
        transfers.set(transferId, currentTransfer);

        // Track compression start
        analyticsService.updatePerformanceMetrics(transferId, { compressionStartTime: Date.now() });
        analyticsService.trackEvent(AnalyticsEventType.COMPRESSION_STARTED, { fileName: currentTransfer.originalName }, transferId);

        try {
          // Perform real ZMT compression with multi-script support
          const { compressedS3Key, compressionRatio, compressedSize, compressionTime, compressionMethod } = await compressFileWithZMT(currentTransfer.s3Key!, transferId, currentTransfer.originalName);

          const finalTransfer = transfers.get(transferId);
          if (finalTransfer) {
            finalTransfer.status = 'ready';
            finalTransfer.downloadUrl = `/api/download/${transferId}`;
            finalTransfer.compressionRatio = compressionRatio;
            finalTransfer.compressedS3Key = compressedS3Key;
            finalTransfer.compressedSize = compressedSize;
            // Store compression metadata
            finalTransfer.compressionTime = compressionTime;
            finalTransfer.compressionMethod = compressionMethod;
            transfers.set(transferId, finalTransfer);

            // Track compression completion
            analyticsService.updatePerformanceMetrics(transferId, {
              compressionEndTime: Date.now(),
              compressedFileSize: compressedSize
            });
            analyticsService.trackEvent(
              AnalyticsEventType.COMPRESSION_COMPLETED,
              {
                fileName: finalTransfer.originalName,
                compressionRatio,
                originalSize: finalTransfer.size,
                compressedSize
              },
              transferId
            );
          }
        } catch (error) {
          console.error('Compression failed:', error);
          const errorTransfer = transfers.get(transferId);
          if (errorTransfer) {
            errorTransfer.status = 'error';
            transfers.set(transferId, errorTransfer);

            // Track compression failure
            analyticsService.trackEvent(
              AnalyticsEventType.COMPRESSION_FAILED,
              {
                fileName: errorTransfer.originalName,
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
              },
              transferId
            );
          }
        }
      }
    }, 1000); // 1 second upload processing

    res.json({
      transferId,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get transfer status
app.get('/api/transfer/:transferId/status', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  res.json({
    transferId: transfer.transferId,
    status: transfer.status,
    downloadUrl: transfer.downloadUrl,
    compressionRatio: transfer.compressionRatio,
    compressedSize: transfer.compressedSize,
    filename: transfer.originalName,
    size: transfer.size
  });
});

// Download endpoint - compressed file (default)
app.get('/api/download/:transferId', async (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: 'Transfer not ready for download' });
  }

  // Use compressed file if available, otherwise fall back to original
  const s3Key = transfer.compressedS3Key || transfer.s3Key;
  const bucket = transfer.compressedS3Key ? COMPRESSED_BUCKET : UPLOAD_BUCKET;

  if (!s3Key) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Track download start
  analyticsService.updatePerformanceMetrics(transferId, { downloadStartTime: Date.now() });
  analyticsService.trackEvent(
    AnalyticsEventType.DOWNLOAD_STARTED,
    {
      fileName: transfer.originalName,
      fileSize: transfer.size,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip
    },
    transferId
  );

  try {
    // Get file from S3
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucket,
      Key: s3Key,
    });

    const response = await s3Client.send(getObjectCommand);

    // Set headers for file download
    const filename = transfer.compressedS3Key ? `${transfer.originalName}.zmt` : transfer.originalName;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', response.ContentType || 'application/octet-stream');
    res.setHeader('Content-Length', response.ContentLength?.toString() || '0');

    // Stream the file from S3
    if (response.Body) {
      const body = response.Body as any;

      // Handle client disconnect
      req.on('close', () => {
        if (body && typeof body.destroy === 'function') {
          body.destroy();
        }
      });

      // Track download completion
      body.on('end', () => {
        analyticsService.updatePerformanceMetrics(transferId, { downloadEndTime: Date.now() });
        analyticsService.trackEvent(
          AnalyticsEventType.DOWNLOAD_COMPLETED,
          {
            fileName: transfer.originalName,
            fileSize: transfer.size
          },
          transferId
        );
      });

      // Track download errors
      body.on('error', (error: any) => {
        console.error('S3 stream error:', error);
        analyticsService.trackEvent(
          AnalyticsEventType.DOWNLOAD_FAILED,
          {
            fileName: transfer.originalName,
            errorMessage: error.message
          },
          transferId
        );
        if (!res.headersSent) {
          res.status(500).json({ error: 'Stream error' });
        }
      });

      // Handle response errors
      res.on('error', (error: any) => {
        console.error('Response stream error:', error);
        if (body && typeof body.destroy === 'function') {
          body.destroy();
        }
      });

      body.pipe(res);
    } else {
      return res.status(404).json({ error: 'File content not found' });
    }
  } catch (error: any) {
    console.error('Download error:', error);
    analyticsService.trackEvent(
      AnalyticsEventType.DOWNLOAD_FAILED,
      {
        fileName: transfer.originalName,
        errorMessage: error.message
      },
      transferId
    );
    return res.status(500).json({ error: 'Failed to download file' });
  }
});

// Download original (uncompressed) file endpoint
app.get('/api/download/:transferId/original', async (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: 'Transfer not ready for download' });
  }

  try {
    let s3Key: string;
    let bucket: string;
    let downloadFileName: string;

    // If we have a compressed file, we need to decompress it first
    if (transfer.compressedS3Key) {
      // Decompress the ZMT file from S3
      console.log(`Decompressing ${transfer.compressedS3Key} for original download`);
      const extractedFiles = await decompressZMTFile(transfer.compressedS3Key, transferId);

      if (extractedFiles.length === 0) {
        return res.status(500).json({ error: 'Failed to decompress file' });
      }

      // Use the first extracted file (should be the original file)
      const extractedFileName = extractedFiles[0];
      s3Key = `decompressed/${transferId}/${extractedFileName}`;
      bucket = DECOMPRESSED_BUCKET;
      downloadFileName = transfer.originalName; // Use original name without .zmt extension

    } else if (transfer.s3Key) {
      // Use original file directly if no compressed version exists
      s3Key = transfer.s3Key;
      bucket = UPLOAD_BUCKET;
      downloadFileName = transfer.originalName;
    } else {
      return res.status(404).json({ error: 'Original file not found' });
    }

    // Track download analytics
    analyticsService.trackEvent(
      AnalyticsEventType.DOWNLOAD_STARTED,
      {
        fileName: transfer.originalName,
        downloadType: 'original',
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      },
      transferId
    );

    // Get file from S3
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucket,
      Key: s3Key,
    });

    const response = await s3Client.send(getObjectCommand);

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${downloadFileName}"`);
    res.setHeader('Content-Type', response.ContentType || 'application/octet-stream');
    res.setHeader('Content-Length', response.ContentLength?.toString() || '0');

    // Stream the file from S3
    if (response.Body) {
      const body = response.Body as any;

      // Handle client disconnect
      req.on('close', () => {
        if (body && typeof body.destroy === 'function') {
          body.destroy();
        }
      });

      // Track download completion
      body.on('end', () => {
        analyticsService.updatePerformanceMetrics(transferId, { downloadEndTime: Date.now() });
        analyticsService.trackEvent(
          AnalyticsEventType.DOWNLOAD_COMPLETED,
          {
            fileName: transfer.originalName,
            downloadType: 'original',
            fileSize: transfer.size
          },
          transferId
        );
      });

      body.on('error', (error: any) => {
        console.error('Download stream error:', error);
        analyticsService.trackEvent(
          AnalyticsEventType.ERROR_OCCURRED,
          {
            errorType: 'download_stream_error',
            errorMessage: error.message,
            fileName: transfer.originalName
          },
          transferId
        );
        if (!res.headersSent) {
          res.status(500).json({ error: 'Stream error' });
        }
      });

      // Handle response errors
      res.on('error', (error: any) => {
        console.error('Response stream error:', error);
        if (body && typeof body.destroy === 'function') {
          body.destroy();
        }
      });

      body.pipe(res);
    } else {
      return res.status(404).json({ error: 'File content not found' });
    }

  } catch (error) {
    console.error('Original download error:', error);
    analyticsService.trackEvent(
      AnalyticsEventType.ERROR_OCCURRED,
      {
        errorType: 'original_download_failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        fileName: transfer.originalName
      },
      transferId
    );
    res.status(500).json({ error: 'Failed to prepare original file for download' });
  }
});

// Decompress endpoint
app.post('/api/decompress', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No ZMT file uploaded' });
    }

    const decompressId = uuidv4();
    const outputDir = path.join(__dirname, '../decompressed', decompressId);

    // Create output directory
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Decompress the ZMT file
    const extractedFiles = await decompressZMTFile(req.file.path, outputDir);

    // Clean up uploaded ZMT file
    fs.unlinkSync(req.file.path);

    res.json({
      decompressId,
      extractedFiles,
      message: 'File decompressed successfully'
    });
  } catch (error) {
    console.error('Decompression error:', error);
    res.status(500).json({ error: 'Decompression failed' });
  }
});

// Download decompressed file
app.get('/api/decompress/:decompressId/:filename', (req, res) => {
  const { decompressId, filename } = req.params;
  const filePath = path.join(__dirname, '../decompressed', decompressId, filename);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: 'Decompressed file not found' });
  }

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the decompressed file
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
});

// Get all transfers with filtering, sorting, and pagination
app.get('/api/transfers', (req, res) => {
  try {
    const {
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = '1',
      limit = '50',
      startDate,
      endDate
    } = req.query;

    let transferList = Array.from(transfers.values());

    // Filter by status
    if (status && status !== 'all') {
      transferList = transferList.filter(transfer => transfer.status === status);
    }

    // Filter by search query (filename)
    if (search) {
      const searchLower = (search as string).toLowerCase();
      transferList = transferList.filter(transfer =>
        transfer.originalName.toLowerCase().includes(searchLower)
      );
    }

    // Filter by date range
    if (startDate) {
      const start = new Date(startDate as string).getTime();
      transferList = transferList.filter(transfer =>
        transfer.createdAt.getTime() >= start
      );
    }
    if (endDate) {
      const end = new Date(endDate as string).getTime();
      transferList = transferList.filter(transfer =>
        transfer.createdAt.getTime() <= end
      );
    }

    // Sort transfers
    transferList.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'filename':
          aValue = a.originalName.toLowerCase();
          bValue = b.originalName.toLowerCase();
          break;
        case 'size':
          aValue = a.size;
          bValue = b.size;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'createdAt':
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTransfers = transferList.slice(startIndex, endIndex);

    // Format response
    const formattedTransfers = paginatedTransfers.map(transfer => ({
      transferId: transfer.transferId,
      status: transfer.status,
      downloadUrl: transfer.downloadUrl,
      compressionRatio: transfer.compressionRatio,
      filename: transfer.originalName,
      size: transfer.size,
      createdAt: transfer.createdAt,
      expiresAt: transfer.expiresAt,
      downloadCount: transfer.downloadCount || 0,
      downloadLimit: transfer.downloadLimit
    }));

    res.json({
      transfers: formattedTransfers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: transferList.length,
        totalPages: Math.ceil(transferList.length / limitNum),
        hasNext: endIndex < transferList.length,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error getting transfers:', error);
    res.status(500).json({ error: 'Failed to get transfers' });
  }
});

// Generate share link
app.post('/api/link', (req, res) => {
  const { transferId, expirationHours, downloadLimit, password } = req.body;

  if (!transferId) {
    return res.status(400).json({ error: 'Transfer ID is required' });
  }

  const transfer = transfers.get(transferId);
  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: `Transfer is not ready for sharing. Current status: ${transfer.status}` });
  }

  // Update transfer with link settings
  if (expirationHours) {
    transfer.expiresAt = Date.now() + (expirationHours * 60 * 60 * 1000);
  }
  if (downloadLimit !== undefined) {
    transfer.downloadLimit = downloadLimit;
  }
  if (password) {
    transfer.password = password; // In production, this should be hashed
  }

  // Generate share URL - use frontend URL in development, backend URL in production
  const frontendUrl = process.env.FRONTEND_DEV_URL || `${req.protocol}://${req.get('host')}`;
  const shareUrl = `${frontendUrl}/share/${transferId}`;

  res.json({
    transferId,
    shareUrl,
    expiresAt: transfer.expiresAt,
    downloadLimit: transfer.downloadLimit,
  });
});

// Get transfer info for shared link
app.get('/api/transfer/:transferId/info', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  // Check if transfer is expired
  if (transfer.expiresAt && Date.now() > transfer.expiresAt) {
    return res.status(410).json({ error: 'Transfer has expired' });
  }

  // Return public transfer info (without sensitive data)
  const publicInfo = {
    transferId: transfer.transferId,
    status: transfer.status,
    originalName: transfer.originalName,
    size: transfer.size,
    originalSize: transfer.size, // Add originalSize field for frontend compatibility
    compressedSize: transfer.compressedSize,
    compressionRatio: transfer.compressionRatio,
    createdAt: transfer.createdAt,
    expiresAt: transfer.expiresAt,
    downloadCount: transfer.downloadCount || 0,
    downloadLimit: transfer.downloadLimit,
    hasPassword: !!transfer.password,
  };

  res.json(publicInfo);
});

// Delete transfer
app.delete('/api/transfer/:transferId', async (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  try {
    // Delete original file from S3 if it exists
    if (transfer.s3Key) {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: UPLOAD_BUCKET,
        Key: transfer.s3Key,
      });
      await s3Client.send(deleteCommand);
    }

    // Delete compressed file from S3 if it exists
    if (transfer.compressedS3Key) {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: COMPRESSED_BUCKET,
        Key: transfer.compressedS3Key,
      });
      await s3Client.send(deleteCommand);
    }

    transfers.delete(transferId);
    res.json({ message: 'Transfer deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting files from S3:', error);
    // Still delete from memory even if S3 deletion fails
    transfers.delete(transferId);
    res.json({ message: 'Transfer deleted from memory, but S3 cleanup may have failed' });
  }
});

// Bulk delete transfers
app.post('/api/transfers/bulk-delete', async (req, res) => {
  try {
    const { transferIds } = req.body;

    if (!Array.isArray(transferIds) || transferIds.length === 0) {
      return res.status(400).json({ error: 'Transfer IDs array is required' });
    }

    const deletedTransfers = [];
    const failedDeletes = [];

    for (const transferId of transferIds) {
      const transfer = transfers.get(transferId);

      if (!transfer) {
        failedDeletes.push({ transferId, error: 'Transfer not found' });
        continue;
      }

      try {
        // Delete original file from S3 if it exists
        if (transfer.s3Key) {
          const deleteCommand = new DeleteObjectCommand({
            Bucket: UPLOAD_BUCKET,
            Key: transfer.s3Key,
          });
          await s3Client.send(deleteCommand);
        }

        // Delete compressed file from S3 if it exists
        if (transfer.compressedS3Key) {
          const deleteCommand = new DeleteObjectCommand({
            Bucket: COMPRESSED_BUCKET,
            Key: transfer.compressedS3Key,
          });
          await s3Client.send(deleteCommand);
        }

        transfers.delete(transferId);
        deletedTransfers.push(transferId);
      } catch (error: any) {
        failedDeletes.push({ transferId, error: 'Failed to delete files from S3' });
      }
    }

    res.json({
      message: `Successfully deleted ${deletedTransfers.length} transfers`,
      deleted: deletedTransfers,
      failed: failedDeletes
    });
  } catch (error) {
    console.error('Bulk delete error:', error);
    res.status(500).json({ error: 'Failed to delete transfers' });
  }
});

// Update transfer metadata
app.patch('/api/transfer/:transferId', (req, res) => {
  try {
    const { transferId } = req.params;
    const { downloadLimit, expirationHours, password } = req.body;

    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    // Update download limit
    if (downloadLimit !== undefined) {
      transfer.downloadLimit = downloadLimit;
    }

    // Update expiration
    if (expirationHours !== undefined) {
      if (expirationHours === 0) {
        transfer.expiresAt = undefined; // Remove expiration
      } else {
        transfer.expiresAt = Date.now() + (expirationHours * 60 * 60 * 1000);
      }
    }

    // Update password
    if (password !== undefined) {
      transfer.password = password || undefined; // In production, this should be hashed
    }

    transfers.set(transferId, transfer);

    res.json({
      message: 'Transfer updated successfully',
      transfer: {
        transferId: transfer.transferId,
        downloadLimit: transfer.downloadLimit,
        expiresAt: transfer.expiresAt,
        hasPassword: !!transfer.password
      }
    });
  } catch (error) {
    console.error('Update transfer error:', error);
    res.status(500).json({ error: 'Failed to update transfer' });
  }
});

// Send email with download link
app.post('/api/email', async (req, res) => {
  try {
    const { transferId, recipientEmail, senderName } = req.body;

    // Validate required fields
    if (!transferId) {
      return res.status(400).json({ error: 'Transfer ID is required' });
    }

    if (!recipientEmail) {
      return res.status(400).json({ error: 'Recipient email is required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return res.status(400).json({ error: 'Invalid email address format' });
    }

    // Get transfer
    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    // Check if transfer is ready for sharing
    if (transfer.status !== 'ready') {
      return res.status(400).json({ error: `Transfer is not ready for sharing. Current status: ${transfer.status}` });
    }

    // Check if transfer is expired
    if (transfer.expiresAt && Date.now() > transfer.expiresAt) {
      return res.status(410).json({ error: 'Transfer has expired' });
    }

    // Check if download limit has been reached
    if (transfer.downloadLimit && (transfer.downloadCount || 0) >= transfer.downloadLimit) {
      return res.status(410).json({ error: 'Download limit has been reached' });
    }

    // Generate download URL - use frontend URL in development, backend URL in production
    const frontendUrl = process.env.FRONTEND_DEV_URL || `${req.protocol}://${req.get('host')}`;
    const downloadUrl = `${frontendUrl}/share/${transferId}`;

    // Prepare email data with file details
    const files = [{
      name: transfer.originalName,
      size: transfer.size,
      originalSize: transfer.size // For single file, original size is the same
    }];

    const emailData: EmailShareRequest = {
      transferId,
      recipientEmail,
      senderName,
      shareLink: downloadUrl,
      files,
      totalSize: transfer.size,
      totalOriginalSize: transfer.size,
      compressionRatio: transfer.compressionRatio,
      expiresAt: transfer.expiresAt ? new Date(transfer.expiresAt).toISOString() : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      downloadLimit: transfer.downloadLimit || 10,
    };

    // Send email
    const emailResult = await sendShareEmail(emailData);

    if (!emailResult.success) {
      // Track email failure
      analyticsService.trackEvent(
        AnalyticsEventType.ERROR_OCCURRED,
        {
          errorType: 'email_send_failed',
          errorMessage: emailResult.error,
          recipientEmail,
          fileName: transfer.originalName
        },
        transferId
      );
      return res.status(500).json({ error: `Failed to send email: ${emailResult.error}` });
    }

    // Track successful email send
    analyticsService.trackEvent(
      AnalyticsEventType.SHARE_EMAIL_SENT,
      {
        recipientEmail,
        fileName: transfer.originalName,
        fileSize: transfer.size,
        compressionRatio: transfer.compressionRatio
      },
      transferId
    );

    res.json({
      success: true,
    });

  } catch (error) {
    console.error('Email endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Analytics endpoints
app.get('/api/analytics/summary', (req, res) => {
  try {
    const timeRange = req.query.timeRange ? {
      start: parseInt(req.query.start as string),
      end: parseInt(req.query.end as string)
    } : undefined;

    const summary = analyticsService.getAnalyticsSummary(timeRange);
    res.json(summary);
  } catch (error) {
    console.error('Analytics summary error:', error);
    res.status(500).json({ error: 'Failed to get analytics summary' });
  }
});

app.get('/api/analytics/events', (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    const events = analyticsService.getRecentEvents(limit);
    res.json(events);
  } catch (error) {
    console.error('Analytics events error:', error);
    res.status(500).json({ error: 'Failed to get analytics events' });
  }
});

app.get('/api/analytics/performance/:transferId', (req, res) => {
  try {
    const { transferId } = req.params;
    const metrics = analyticsService.getPerformanceMetrics(transferId);

    if (!metrics) {
      return res.status(404).json({ error: 'Performance metrics not found' });
    }

    res.json(metrics);
  } catch (error) {
    console.error('Performance metrics error:', error);
    res.status(500).json({ error: 'Failed to get performance metrics' });
  }
});

// Get transfer statistics
app.get('/api/transfers/stats', (_req, res) => {
  try {
    const transferList = Array.from(transfers.values());

    const stats = {
      total: transferList.length,
      byStatus: {
        uploading: transferList.filter(t => t.status === 'uploading').length,
        compressing: transferList.filter(t => t.status === 'compressing').length,
        ready: transferList.filter(t => t.status === 'ready').length,
        error: transferList.filter(t => t.status === 'error').length
      },
      totalSize: transferList.reduce((acc, t) => acc + t.size, 0),
      totalCompressedSize: transferList.reduce((acc, t) => acc + (t.compressedS3Key ? (t.compressedSize || 0) : 0), 0),
      averageCompressionRatio: transferList.length > 0
        ? transferList.reduce((acc, t) => acc + (t.compressionRatio || 0), 0) / transferList.length
        : 0,
      totalDownloads: transferList.reduce((acc, t) => acc + (t.downloadCount || 0), 0),
      recentActivity: transferList
        .filter(t => t.createdAt.getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000)) // Last 7 days
        .length,
      storageUsed: transferList.reduce((acc, t) => {
        let size = t.size; // Original file size
        if (t.compressedS3Key && t.compressedSize) {
          size += t.compressedSize; // Add compressed file size
        }
        return acc + size;
      }, 0)
    };

    res.json(stats);
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({ error: 'Failed to get transfer statistics' });
  }
});

// Health check
app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// File download endpoint with custom cache headers based on file type
app.get('/api/download/:transferId/:filename', async (req, res) => {
    const { transferId, filename } = req.params;

    // Validate transfer exists
    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    if (transfer.status !== 'ready') {
      return res.status(400).json({ error: 'Transfer not ready for download' });
    }

    // Get file extension for cache policy
    const fileExt = path.extname(filename).toLowerCase();

    // Set custom cache headers based on file type
    if (fileExt === '.zmt') {
      // ZMT files - medium-term cache, no compression
      res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'application/octet-stream');
    } else if (['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'].includes(fileExt)) {
      // Archive files - medium-term cache, no compression
      res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'application/octet-stream');
    } else if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(fileExt)) {
      // Document files - daily cache with range support
      res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Vary', 'Accept-Encoding');
    } else if (['.mp4', '.avi', '.mov', '.wmv', '.mp3', '.wav', '.flac', '.aac'].includes(fileExt)) {
      // Media files - long-term cache with range support for streaming
      res.setHeader('Cache-Control', 'public, max-age=2592000, stale-while-revalidate=86400'); // 30 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
    } else if (['.html', '.htm'].includes(fileExt)) {
      // HTML files - short cache with revalidation
      res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate, stale-while-revalidate=60'); // 5 minutes
      res.setHeader('Vary', 'Accept-Encoding');
    } else if (['.txt', '.log', '.csv', '.json', '.xml'].includes(fileExt)) {
      // Text files - hourly cache with compression
      res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
      res.setHeader('Vary', 'Accept-Encoding');
    } else {
      // Default cache for unknown file types
      res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
      res.setHeader('Vary', 'Accept-Encoding');
    }

    // Set security and download headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Track download analytics
    analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_STARTED, {
      filename,
      fileExtension: fileExt,
      userAgent: req.get('User-Agent'),
      fileSize: transfer.size
    }, transferId);

    // Use compressed file if available, otherwise fall back to original
    const s3Key = transfer.compressedS3Key || transfer.s3Key;
    const bucket = transfer.compressedS3Key ? COMPRESSED_BUCKET : UPLOAD_BUCKET;

    if (!s3Key) {
      return res.status(404).json({ error: 'File not found' });
    }

  try {
    // Get file from S3
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucket,
      Key: s3Key,
    });

    const response = await s3Client.send(getObjectCommand);

      // Set headers for file download
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Type', response.ContentType || 'application/octet-stream');
      res.setHeader('Content-Length', response.ContentLength?.toString() || '0');

      // Stream the file from S3
      if (response.Body) {
        const body = response.Body as any;

        // Handle client disconnect
        req.on('close', () => {
          if (body && typeof body.destroy === 'function') {
            body.destroy();
          }
        });

        body.on('end', () => {
          // Track successful download
          analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_COMPLETED, {
            filename,
            fileExtension: fileExt,
            fileSize: transfer.size
          }, transferId);

          // Update download count if limit is set
          if (transfer.downloadLimit !== undefined) {
            transfer.downloadCount = (transfer.downloadCount || 0) + 1;
            transfers.set(transferId, transfer);
          }
        });

        body.on('error', (error: any) => {
          console.error('Download error:', error);
          analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_FAILED, {
            filename,
            error: error.message
          }, transferId);
          if (!res.headersSent) {
            res.status(500).json({ error: 'Stream error' });
          }
        });

        // Handle response errors
        res.on('error', (error: any) => {
          console.error('Response stream error:', error);
          if (body && typeof body.destroy === 'function') {
            body.destroy();
          }
        });

        body.pipe(res);
      } else {
        return res.status(404).json({ error: 'File content not found' });
      }

    } catch (error: any) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Failed to process download request' });
  }
});

// Serve static files from frontend build (if available)
const frontendBuildPath = path.join(__dirname, '../../../packages/frontend/dist');
if (fs.existsSync(frontendBuildPath)) {
  // Configure caching for different asset types
  app.use('/assets', (req, res, next) => {
    // Versioned assets (JS, CSS, images with hashes) - long cache
    if (req.path.match(/\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/)) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
      res.setHeader('Expires', new Date(Date.now() + 31536000000).toUTCString());
    }
    next();
  }, express.static(path.join(frontendBuildPath, 'assets')));

  // Serve other static files with custom cache policies
  app.use(express.static(frontendBuildPath, {
    setHeaders: (res, filePath) => {
      // Set common headers for all files
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');

      if (filePath.endsWith('.html')) {
        // HTML files - short cache for updates with revalidation
        res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate, stale-while-revalidate=60');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(js|css)$/)) {
        // Non-versioned JS/CSS - medium cache
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(png|jpg|jpeg|gif|svg|ico|webp|avif)$/)) {
        // Images - medium cache with immutable for hashed files
        const isVersioned = filePath.includes('-') && filePath.match(/[a-f0-9]{8,}/);
        if (isVersioned) {
          res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
        } else {
          res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
        }
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(woff|woff2|ttf|eot)$/)) {
        // Fonts - long cache with CORS headers
        res.setHeader('Cache-Control', 'public, max-age=2592000, immutable'); // 30 days
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
      } else if (filePath.match(/\.(zmt)$/)) {
        // ZMT compressed files - medium-term cache with range support
        res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't double-compress
      } else if (filePath.match(/\.(zip|rar|7z|tar|gz|bz2)$/)) {
        // Archive files - medium cache with range support
        res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't compress archives
      } else if (filePath.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/)) {
        // Document files - medium cache with range support
        res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(mp4|avi|mov|wmv|flv|webm|mkv|mp3|wav|flac|aac)$/)) {
        // Media files - long cache with range support for streaming
        res.setHeader('Cache-Control', 'public, max-age=2592000, stale-while-revalidate=86400'); // 30 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't compress media
      } else if (filePath.match(/\.(txt|log|csv|json|xml)$/)) {
        // Text files - short cache with compression
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
        res.setHeader('Vary', 'Accept-Encoding');
      } else {
        // Default cache for unknown file types
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
        res.setHeader('Vary', 'Accept-Encoding');
      }
    }
  }));

  // Handle client-side routing - serve index.html for non-API routes
  app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    // Set cache headers for HTML
    res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate');
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  });
} else {
  // Development fallback - redirect share links to frontend dev server
  app.get('/share/:transferId', (req, res) => {
    const { transferId } = req.params;
    // In development, redirect to frontend dev server
    const frontendDevUrl = process.env.FRONTEND_DEV_URL || 'http://localhost:5174';
    res.redirect(`${frontendDevUrl}/share/${transferId}`);
  });

  // For other non-API routes in development
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    const frontendDevUrl = process.env.FRONTEND_DEV_URL || 'http://localhost:5174';
    res.redirect(`${frontendDevUrl}${req.path}`);
  });
}

// Start server
const server = app.listen(port, () => {
  console.log(`FastTransfer backend server running on port ${port}`);
  console.log(`API endpoints available at http://localhost:${port}/api`);
  console.log(`Server configured for large file uploads up to 100GB`);

  const frontendBuildExists = fs.existsSync(frontendBuildPath);
  if (frontendBuildExists) {
    console.log(`Serving frontend from: ${frontendBuildPath}`);
  } else {
    console.log(`Frontend build not found. Development mode - redirecting to frontend dev server`);
    console.log(`Make sure frontend dev server is running on ${process.env.FRONTEND_DEV_URL || 'http://localhost:5173'}`);
  }

  // Start system monitoring
  analytics.startSystemMonitoring(5); // Monitor every 5 minutes
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Set server timeout for large file uploads (30 minutes)
server.timeout = 30 * 60 * 1000; // 30 minutes
server.keepAliveTimeout = 30 * 60 * 1000; // 30 minutes
server.headersTimeout = 30 * 60 * 1000; // 30 minutes

export default app;
